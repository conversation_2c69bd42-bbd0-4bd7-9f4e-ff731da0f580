"""
Serializers for accounts app.
"""
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from drf_spectacular.utils import extend_schema_serializer, OpenApiExample
from .models import UserAccount, UserDevice, DeviceRegistration, ActivationCode


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            'User Registration',
            value={
                'email': '<EMAIL>',
                'password': 'securepassword123',
                'password_confirm': 'securepassword123',
                'device_id': 'unique-device-id-123',
                'device_name': 'iPhone 13',
                'device_type': 'ios'
            }
        )
    ]
)
class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Сериализатор для регистрации пользователей.
    
    PURPOSE:
      - Валидирует данные регистрации пользователя
      - Создает новый аккаунт пользователя
      - Обеспечивает безопасность паролей
    """
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    device_id = serializers.CharField(write_only=True)
    device_name = serializers.CharField(write_only=True, required=False, allow_blank=True)
    device_type = serializers.CharField(write_only=True, required=False, allow_blank=True)

    class Meta:
        model = UserAccount
        fields = ('email', 'password', 'password_confirm', 'device_id', 'device_name', 'device_type')
        extra_kwargs = {
            'email': {'validators': []},  # Убираем встроенные валидаторы уникальности
        }

    def validate(self, attrs):
        """
        Валидация данных регистрации согласно BACKEND_ROADMAP Этапа 1.

        PURPOSE:
          - Проверяет соответствие паролей
          - Валидирует уникальность email (для 409 Conflict)
          - Проверяет уникальность device_id для пользователя

        RAISES:
          - ValidationError: Для 400 Bad Request (невалидные данные)
          - Специальная обработка для 409 Conflict в view
        """
        # Проверка соответствия паролей
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError({
                "password_confirm": ["Passwords don't match."]
            })

        # Проверка уникальности email - помечаем для 409 Conflict
        if UserAccount.objects.filter(email=attrs['email']).exists():
            # Используем специальный маркер для обработки в view
            raise serializers.ValidationError({
                "_email_exists": ["User with this email already exists."],
                "email": ["User with this email already exists."]
            })

        return attrs

    def validate_email(self, value):
        """Валидация формата email."""
        if not value or '@' not in value or '.' not in value.split('@')[-1]:
            raise serializers.ValidationError("Enter a valid email address.")
        return value

    def validate_password(self, value):
        """Валидация пароля согласно требованиям."""
        if not value:
            raise serializers.ValidationError("This field is required.")
        if len(value) < 8:
            raise serializers.ValidationError("This password is too short. It must contain at least 8 characters.")
        return value

    def validate_device_id(self, value):
        """Валидация device_id."""
        if not value:
            raise serializers.ValidationError("This field is required.")
        if not value.strip():
            raise serializers.ValidationError("This field cannot be blank.")

        # Проверяем глобальную уникальность device_id (для всех пользователей)
        from .models import UserDevice
        if UserDevice.objects.filter(device_id=value).exists():
            raise serializers.ValidationError("Device with this ID already exists in the system.")

        return value

    def create(self, validated_data):
        """Создание пользователя с хэшированием пароля."""
        # Удаляем поля, которые не относятся к модели UserAccount
        device_data = {
            'device_id': validated_data.pop('device_id'),
            'device_name': validated_data.pop('device_name', ''),
            'device_type': validated_data.pop('device_type', ''),
        }
        validated_data.pop('password_confirm')
        
        # Создаем пользователя
        user = UserAccount.objects.create_user(
            username=validated_data['email'],  # Django требует username
            **validated_data
        )
        
        # Сохраняем данные устройства для дальнейшего использования
        user._device_data = device_data
        
        return user


class UserLoginSerializer(serializers.Serializer):
    """
    Сериализатор для аутентификации пользователей.
    
    PURPOSE:
      - Валидирует учетные данные пользователя
      - Обеспечивает безопасную аутентификацию
      - Возвращает данные аутентифицированного пользователя
    """
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    device_id = serializers.CharField(required=False, allow_blank=True)

    def validate(self, attrs):
        """Валидация учетных данных."""
        email = attrs.get('email')
        password = attrs.get('password')

        if email and password:
            user = authenticate(username=email, password=password)
            
            if not user:
                raise serializers.ValidationError('Invalid email or password.')
            
            if not user.is_active:
                raise serializers.ValidationError('User account is disabled.')
            
            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError('Must include email and password.')


class UserProfileSerializer(serializers.ModelSerializer):
    """
    Сериализатор для профиля пользователя.
    
    PURPOSE:
      - Отображает информацию профиля пользователя
      - Позволяет обновлять определенные поля профиля
      - Скрывает чувствительную информацию
    """
    devices_count = serializers.SerializerMethodField()
    
    class Meta:
        model = UserAccount
        fields = ('id', 'email', 'phone', 'is_email_verified', 'created_at', 'devices_count')
        read_only_fields = ('id', 'email', 'is_email_verified', 'created_at')

    def get_devices_count(self, obj) -> int:
        """Возвращает количество активных устройств пользователя."""
        return obj.devices.filter(is_active=True).count()


class UserDeviceSerializer(serializers.ModelSerializer):
    """
    Сериализатор для устройств пользователя.
    
    PURPOSE:
      - Отображает информацию об устройствах пользователя
      - Позволяет управлять устройствами
      - Обеспечивает валидацию данных устройств
    """
    
    class Meta:
        model = UserDevice
        fields = ('id', 'device_id', 'device_name', 'device_type', 'last_seen', 'is_active', 'created_at')
        read_only_fields = ('id', 'last_seen', 'created_at')

    def validate_device_id(self, value):
        """Валидация уникальности device_id для пользователя."""
        user = self.context['request'].user
        if UserDevice.objects.filter(user=user, device_id=value).exists():
            raise serializers.ValidationError("Device with this ID already exists for this user.")
        return value


# Response serializers for Swagger documentation
class TokenResponseSerializer(serializers.Serializer):
    """Serializer for JWT token response."""
    access = serializers.CharField()
    refresh = serializers.CharField()


class UserResponseSerializer(serializers.Serializer):
    """Serializer for user response data."""
    id = serializers.UUIDField()
    email = serializers.EmailField()
    created_at = serializers.DateTimeField(required=False)


class SubscriptionInfoSerializer(serializers.Serializer):
    """Serializer for subscription info in responses."""
    plan_name = serializers.CharField()
    end_date = serializers.DateTimeField()
    traffic_limit_gb = serializers.IntegerField()


class RegistrationResponseSerializer(serializers.Serializer):
    """Serializer for registration response."""
    success = serializers.BooleanField()
    user = UserResponseSerializer()
    subscription = SubscriptionInfoSerializer()
    tokens = TokenResponseSerializer()


class LoginResponseSerializer(serializers.Serializer):
    """Serializer for login response."""
    success = serializers.BooleanField()
    user = UserResponseSerializer()
    tokens = TokenResponseSerializer()


class ErrorResponseSerializer(serializers.Serializer):
    """Serializer for error responses."""
    error = serializers.CharField()


# Anonymous User Serializers

class DeviceInitSerializer(serializers.Serializer):
    """
    Сериализатор для инициализации устройства (создания анонимного пользователя).

    PURPOSE:
      - Валидирует данные для создания анонимного пользователя
      - Проверяет корректность device_id, device_name, device_type
      - Обеспечивает безопасную инициализацию устройства

    AAG (Actor -> Action -> Goal):
      - Мобильное приложение -> Отправляет данные устройства -> Получает VPN-доступ
      - Система -> Создает анонимного пользователя -> Обеспечивает временный доступ

    CONTRACT:
      PRECONDITIONS:
        - device_id (str): Уникальный идентификатор устройства от клиента
        - device_name (str): Человекочитаемое имя устройства
        - device_type (str): Тип устройства (ios, android, windows, etc.)
      POSTCONDITIONS:
        - Данные валидированы для создания анонимного пользователя
        - device_id проверен на корректность формата
      INVARIANTS:
        - device_id не может быть пустым
        - device_type должен быть из допустимых значений
    """
    device_id = serializers.CharField(
        max_length=255,
        help_text="Уникальный идентификатор устройства"
    )
    device_name = serializers.CharField(
        max_length=100,
        help_text="Название устройства (например, 'iPhone 13')"
    )
    device_type = serializers.ChoiceField(
        choices=[
            ('ios', 'iOS'),
            ('android', 'Android'),
            ('windows', 'Windows'),
            ('macos', 'macOS'),
            ('linux', 'Linux'),
            ('other', 'Other')
        ],
        help_text="Тип устройства"
    )

    def validate_device_id(self, value):
        """Валидация device_id."""
        if not value or not value.strip():
            raise serializers.ValidationError("Device ID не может быть пустым")

        # Проверяем длину
        if len(value.strip()) < 3:
            raise serializers.ValidationError("Device ID должен содержать минимум 3 символа")

        return value.strip()


class DeviceInitResponseSerializer(serializers.Serializer):
    """Сериализатор для ответа инициализации устройства."""
    success = serializers.BooleanField()
    user = UserResponseSerializer()
    subscription = SubscriptionInfoSerializer()
    tokens = TokenResponseSerializer()


class ConvertAnonymousSerializer(serializers.Serializer):
    """
    Сериализатор для конвертации анонимного аккаунта в зарегистрированный.

    PURPOSE:
      - Валидирует данные для конвертации анонимного пользователя
      - Проверяет email, пароль и их соответствие
      - Обеспечивает безопасную конвертацию аккаунта

    AAG (Actor -> Action -> Goal):
      - Анонимный пользователь -> Предоставляет email/пароль -> Получает полноценный аккаунт
      - Система -> Конвертирует аккаунт -> Сохраняет VPN-доступ и историю

    CONTRACT:
      PRECONDITIONS:
        - email (str): Валидный email адрес, не занятый в системе
        - password (str): Пароль, соответствующий требованиям безопасности
        - password_confirm (str): Подтверждение пароля
        - Пользователь должен быть аутентифицирован и иметь is_anonymous=True
      POSTCONDITIONS:
        - Данные валидированы для конвертации
        - Email проверен на уникальность
        - Пароли проверены на соответствие
      INVARIANTS:
        - password == password_confirm
        - email уникален в системе
    """
    email = serializers.EmailField(
        help_text="Email адрес для регистрации"
    )
    password = serializers.CharField(
        write_only=True,
        validators=[validate_password],
        help_text="Пароль для аккаунта"
    )
    password_confirm = serializers.CharField(
        write_only=True,
        help_text="Подтверждение пароля"
    )

    def validate_email(self, value):
        """Валидация email на уникальность."""
        if UserAccount.objects.filter(email=value, is_anonymous=False).exists():
            raise serializers.ValidationError("Пользователь с таким email уже существует")
        return value

    def validate(self, attrs):
        """Валидация соответствия паролей."""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError({
                "password_confirm": ["Пароли не совпадают"]
            })
        return attrs


class ConvertAnonymousResponseSerializer(serializers.Serializer):
    """Сериализатор для ответа конвертации анонимного аккаунта."""
    success = serializers.BooleanField()
    message = serializers.CharField()
    user = UserResponseSerializer()
    tokens = TokenResponseSerializer()


# MVP Device Registration Serializers

@extend_schema_serializer(
    examples=[
        OpenApiExample(
            'Device Registration (Empty Body)',
            value={}
        ),
        OpenApiExample(
            'Device Registration (With Client ID)',
            value={
                'client_generated_device_id': 'my-device-12345',
                'device_name': 'iPhone 15 Pro',
                'device_type': 'ios'
            }
        )
    ]
)
class DeviceRegistrationSerializer(serializers.Serializer):
    """
    Сериализатор для регистрации устройства (MVP).

    PURPOSE:
      - Валидирует опциональные данные устройства
      - Обеспечивает быструю регистрацию без создания полноценного пользователя
      - Поддерживает как пустые запросы, так и с метаданными устройства

    AAG (Actor -> Action -> Goal):
      - Мобильное приложение -> Отправляет данные устройства -> Получает internal_device_id
      - Система -> Создает пользователя в Hiddify -> Возвращает идентификатор для дальнейшего использования

    CONTRACT:
      PRECONDITIONS:
        - Все поля опциональны
        - client_generated_device_id может быть любой строкой
      POSTCONDITIONS:
        - Возвращается уникальный internal_device_id
        - Создается связь с Hiddify Manager
    """
    client_generated_device_id = serializers.CharField(
        max_length=255,
        required=False,
        allow_blank=True,
        help_text="Опциональный идентификатор устройства от клиента"
    )
    device_name = serializers.CharField(
        max_length=100,
        required=False,
        allow_blank=True,
        help_text="Название устройства (например, 'iPhone 15 Pro')"
    )
    device_type = serializers.CharField(
        max_length=50,
        required=False,
        allow_blank=True,
        help_text="Тип устройства (ios, android, windows, macos, linux)"
    )


class DeviceRegistrationResponseSerializer(serializers.Serializer):
    """Сериализатор для ответа регистрации устройства."""
    internal_device_id = serializers.CharField(
        help_text="Уникальный идентификатор устройства, сгенерированный бэкендом"
    )
    message = serializers.CharField(
        help_text="Сообщение о статусе регистрации"
    )


class DeviceConfigRequestSerializer(serializers.Serializer):
    """Сериализатор для валидации заголовка X-Device-ID."""
    device_id = serializers.CharField(
        help_text="Internal device ID, полученный при регистрации"
    )


class VPNConfigResponseSerializer(serializers.Serializer):
    """Сериализатор для ответа с VPN конфигурацией."""
    # Для SingBox конфигурации возвращаем сырой JSON
    pass  # Будет возвращать сырой JSON от Hiddify


# Activation Code Serializers

class ActivationCodeGenerateSerializer(serializers.Serializer):
    """
    Сериализатор для генерации кода активации.

    PURPOSE:
      - Валидирует запрос на генерацию кода активации
      - Не требует входных данных (код генерируется автоматически)
      - Используется для документации API

    AAG (Actor -> Action -> Goal):
      - Аутентифицированный пользователь -> Запрашивает код -> Получает временный код активации
      - Система -> Генерирует уникальный код -> Возвращает код с временем истечения
    """
    pass  # Не требует входных данных


class ActivationCodeResponseSerializer(serializers.Serializer):
    """Сериализатор для ответа с кодом активации."""
    activation_code = serializers.CharField(
        help_text="8-символьный код активации"
    )
    expires_at = serializers.DateTimeField(
        help_text="Время истечения срока действия кода (UTC)"
    )
    expires_in_minutes = serializers.IntegerField(
        help_text="Количество минут до истечения срока действия"
    )


class DeviceActivationSerializer(serializers.Serializer):
    """
    Сериализатор для активации устройства по коду.

    PURPOSE:
      - Валидирует данные для активации нового устройства
      - Проверяет формат кода активации и device_id
      - Обеспечивает безопасную активацию устройств

    AAG (Actor -> Action -> Goal):
      - Новое устройство -> Отправляет код активации -> Получает VPN доступ к аккаунту
      - Система -> Проверяет код -> Связывает устройство с пользователем

    CONTRACT:
      PRECONDITIONS:
        - activation_code (str): 8-символьный код активации
        - device_id (str): Уникальный идентификатор нового устройства
      POSTCONDITIONS:
        - Устройство связывается с аккаунтом пользователя
        - Код активации деактивируется
        - Возвращаются JWT токены для нового устройства
    """
    activation_code = serializers.CharField(
        max_length=8,
        min_length=8,
        help_text="8-символьный код активации"
    )
    device_id = serializers.CharField(
        max_length=255,
        help_text="Уникальный идентификатор нового устройства"
    )
    device_name = serializers.CharField(
        max_length=100,
        required=False,
        allow_blank=True,
        help_text="Название устройства (опционально)"
    )
    device_type = serializers.CharField(
        max_length=50,
        required=False,
        allow_blank=True,
        help_text="Тип устройства (опционально)"
    )

    def validate_activation_code(self, value):
        """Валидация формата кода активации."""
        if not value.isalnum():
            raise serializers.ValidationError("Activation code must contain only letters and numbers.")
        return value.upper()

    def validate_device_id(self, value):
        """Валидация device_id."""
        if not value.strip():
            raise serializers.ValidationError("Device ID cannot be empty.")
        return value


class DeviceActivationResponseSerializer(serializers.Serializer):
    """Сериализатор для ответа активации устройства."""
    success = serializers.BooleanField()
    message = serializers.CharField()
    user = UserResponseSerializer()
    tokens = TokenResponseSerializer()
    device_info = serializers.DictField(
        help_text="Информация о активированном устройстве"
    )


# Device Registration Serializers

class DeviceRegisterSerializer(serializers.Serializer):
    """
    Сериализатор для регистрации анонимного устройства.

    PURPOSE:
      - Валидирует данные для регистрации нового анонимного устройства
      - Поддерживает восстановление сессии через device_secret
      - Обеспечивает создание анонимного пользователя с VPN-доступом

    AAG (Actor -> Action -> Goal):
      - Мобильное приложение -> Регистрирует устройство -> Получает VPN-доступ
      - Система -> Создает анонимного пользователя -> Обеспечивает временный доступ

    CONTRACT:
      PRECONDITIONS:
        - device_id (str): Идентификатор устройства от клиента
        - device_secret (UUID, optional): Секрет для восстановления сессии
      POSTCONDITIONS:
        - Создается или восстанавливается устройство
        - Возвращаются JWT токены и device_secret
      INVARIANTS:
        - device_id не может быть пустым
    """
    device_id = serializers.CharField(
        max_length=255,
        help_text="Идентификатор устройства от клиента"
    )
    device_secret = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Секрет устройства для восстановления сессии (опционально)"
    )

    def validate_device_id(self, value):
        """Валидация device_id."""
        if not value or not value.strip():
            raise serializers.ValidationError("Device ID не может быть пустым")

        if len(value.strip()) < 3:
            raise serializers.ValidationError("Device ID должен содержать минимум 3 символа")

        return value.strip()


class DeviceRegisterResponseSerializer(serializers.Serializer):
    """Сериализатор для ответа регистрации устройства."""
    access_token = serializers.CharField(
        help_text="JWT access токен (срок жизни 30 минут)"
    )
    refresh_token = serializers.CharField(
        help_text="JWT refresh токен (срок жизни 60 дней)"
    )
    device_secret = serializers.UUIDField(
        required=False,
        help_text="Секрет устройства (возвращается только при первой регистрации)"
    )
