"""
URL configuration for accounts app.
"""
from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

urlpatterns = [
    # Authentication
    path('register/', views.register_user, name='register'),
    path('login/', views.login_user, name='login'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # Anonymous Users
    path('device/init/', views.init_device, name='init_device'),
    path('convert-anonymous/', views.convert_anonymous, name='convert_anonymous'),

    # Two-Level Authentication (Stage 1 Core)
    path('activation-code/', views.generate_activation_code, name='generate_activation_code'),
    path('activate/', views.activate_device, name='activate_device'),

    # User Profile
    path('profile/', views.UserProfileView.as_view(), name='user_profile'),

    # User Devices
    path('devices/', views.UserDeviceListView.as_view(), name='user_devices'),
    path('devices/<uuid:pk>/', views.UserDeviceDetailView.as_view(), name='user_device_detail'),

    # Device Registration (Anonymous)
    path('devices/register/', views.register_device, name='register_device'),
]
